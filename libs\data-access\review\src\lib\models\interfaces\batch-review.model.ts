export interface BatchModel {
  batchId: number
  reviewSetId: number
  name: string
  batchStatus: string
  reviewer: string
  totalFiles: number
  remainingFiles: number
  totalGeneratedHtmlFiles: number
}
export interface ReviewSetBasicInfo {
  reviewSetId: number
  reviewSetName: string
  reviewTagId: number
  markTaggedDocsAsReviewed: boolean
}
export interface ModuleLoginModel {
  projectLoginId: number
  reviewSetId?: number
  Module: string
  loginDateTime?: Date
  logOutDateTime?: Date
  updateEstimatedLogout?: boolean
}
export interface MarkAsReviewedRequestModel {
  globalTempTable: string
  fileIds: number[]
  unSelectedFileIds?: number[]
  isBatchSelected?: boolean
}

export interface ReviewStatusRequestModel {
  isFileTagged: boolean
  reviewSetId: number
  currentPageFileIds: number[]
}

export interface FileTaggedResponseModel {
  updatedTagList: FileTagMappingModel
  updatedTagColorList: FileTagColorMappingModel
  reviewedFileIds: number[]
}

export interface FileTagMappingModel {
  fileId: number
  tags: string
}

export interface FileTagColorMappingModel {
  fileId: number
  tagNameColors: string
}
