import { inject, Injectable } from '@angular/core'
import { ActivatedRoute } from '@angular/router'
import {
  DocumentCodingFacade,
  DocumentTagFacade,
  SaveDocumentCodingPayloadModel,
  SaveDocumentTagPayloadModel,
  SaveTagPayloadModel,
} from '@venio/data-access/document-utility'
import {
  DocumentsFacade,
  FileTaggedResponseModel,
  MarkAsReviewedRequestModel,
  ReviewsetFacade,
  ReviewSetStateService,
  SearchFacade,
  SearchResponseData,
  SearchResultFacade,
} from '@venio/data-access/review'
import { PageControlActionType } from '@venio/shared/models/constants'
import { combineLatest, filter, switchMap, take } from 'rxjs'

@Injectable({ providedIn: 'root' })
export class DocumentTagUtilityService {
  private readonly activatedRoute = inject(ActivatedRoute)
  private get docShareToken(): string {
    return (this.activatedRoute.snapshot.queryParams['docShareToken'] ||
      '') as string
  }

  constructor(
    private documentsFacade: DocumentsFacade,
    private documentTagFacade: DocumentTagFacade,
    private documentCodingFacade: DocumentCodingFacade,
    private searchResultFacade: SearchResultFacade,
    private searchFacade: SearchFacade,
    private reviewSetFacade: ReviewsetFacade,
    private reviewSetState: ReviewSetStateService
  ) {}

  public saveTag(
    projectId: number,
    navigationType: PageControlActionType
  ): void {
    combineLatest([
      this.documentsFacade.selectDocumentPayloadData,
      this.documentTagFacade.selectDocumentTagPayloadData$,
      this.documentCodingFacade.selectDocumentCodingPayloadData$,
      this.searchResultFacade.getSearchResultFileIds,
      this.searchFacade.getSearchTempTables$,
      this.documentTagFacade.selectIsTagDataModified$,
      this.documentCodingFacade.selectIsCodingDataModified$,
    ])
      .pipe(
        filter(
          ([
            documentPayload,
            documentTagPayload,
            documentCodingPayload,
            currentPageFileIds,
            tempTables,
          ]) =>
            Boolean(
              documentPayload &&
                documentTagPayload &&
                documentCodingPayload &&
                currentPageFileIds &&
                currentPageFileIds.length > 0 &&
                tempTables
            )
        ),
        take(1)
      )
      .subscribe(
        ([
          documentPayload,
          documentTagPayload,
          documentCodingPayload,
          currentPageFileIds,
          tempTables,
          isTagDataModified,
          isCodingDataModified,
        ]) => {
          const saveDocumentTagPayload: SaveDocumentTagPayloadModel = {
            projectTags: documentTagPayload.projectTags,
            tagSettings: documentTagPayload.projectTagSettings,
            selectChangedTags: documentTagPayload.changedTags,
          }

          const saveDocumentCodingPayload: SaveDocumentCodingPayloadModel = {
            fieldCoding: documentCodingPayload.fieldCodingModel,
            updatedCodingFieldInfoIds:
              documentCodingPayload.updatedCodingFieldInfoIds,
          }

          const tagPayload: SaveTagPayloadModel = {
            documentPayload: documentPayload,
            documentTagPayload: saveDocumentTagPayload,
            documentCodingTagPayload: saveDocumentCodingPayload,
            currentPageFileIds: currentPageFileIds,
            projectId: projectId,
            navigationType: navigationType,
            searchResultTempTable: tempTables?.searchResultTempTable,
            documentShareToken: this.docShareToken,
          }

          // If tag / coding data is modified, save the data else we will navigate to next / previous document.
          if (isTagDataModified || isCodingDataModified) {
            this.documentTagFacade.saveTag(tagPayload)
            if (
              this.reviewSetState.reviewSetId() > 0 &&
              this.reviewSetState.reviewSetBasicInfo().markTaggedDocsAsReviewed
            ) {
              const reviewPayload: MarkAsReviewedRequestModel = {
                fileIds: documentPayload.selectedDocuments,
                globalTempTable: tempTables?.searchResultTempTable,
                isBatchSelected: documentPayload.isBatchSelected,
                unSelectedFileIds: documentPayload.unselectedDocuments,
              }
              this.handleMarkAsReviewedBulk(projectId, reviewPayload)
            }
          } else if (navigationType === PageControlActionType.NEXT_PAGE) {
            this.documentsFacade.setDocumentNavigation = navigationType
          } else if (navigationType === PageControlActionType.PREV_PAGE)
            this.documentsFacade.setDocumentNavigation = navigationType
        }
      )
  }

  private handleMarkAsReviewedBulk(
    projectId: number,
    reviewPayload: MarkAsReviewedRequestModel
  ): void {
    this.reviewSetFacade
      .markAsReviewedBulk$(
        projectId,
        this.reviewSetState.reviewSetId(),
        this.reviewSetState.batchId(),
        reviewPayload
      )
      .pipe(
        switchMap(() => {
          return this.searchResultFacade.getSearchResultFileIds.pipe(
            take(1),
            switchMap((currentPageFileIds) => {
              return this.reviewSetFacade
                .reviewedFileStatus$<FileTaggedResponseModel>(projectId, {
                  reviewSetId: this.reviewSetState.reviewSetId(),
                  currentPageFileIds: currentPageFileIds,
                  isFileTagged: false,
                })
                .pipe(
                  switchMap((response: FileTaggedResponseModel) => {
                    // Use the reviewedFileIds from the response
                    return this.searchResultFacade.getSearchResultEntityByFileIds(
                      response.reviewedFileIds
                    )
                  })
                )
            })
          )
        }),
        take(1)
      )
      .subscribe((selectedFileData: SearchResponseData[]) => {
        const updatedRows = selectedFileData.map((row) => ({
          ...row,
          metadata: row.metadata.map((item) => {
            if (item.key === '__isReviewed') {
              return { ...item, value: 'Yes' }
            } else return item
          }),
        }))
        this.searchResultFacade.updateManySearchResult(updatedRows)
        this.reviewSetFacade.checkBatchReviewCompletedAction$.next(
          this.reviewSetState.batchId()
        )
      })
  }
}
