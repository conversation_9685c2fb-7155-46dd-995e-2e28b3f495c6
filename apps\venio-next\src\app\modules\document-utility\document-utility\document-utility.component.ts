import { CommonModule, isPlatformBrowser, Location } from '@angular/common'
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  computed,
  inject,
  Inject,
  Injector,
  OnDestroy,
  OnInit,
  PLATFORM_ID,
  Signal,
  Type,
  ViewContainerRef,
  WritableSignal,
  signal,
} from '@angular/core'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import {
  ExpansionPanelModule,
  LayoutModule,
  PanelBarModule,
} from '@progress/kendo-angular-layout'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import {
  PageControlActionType,
  ParentChildMessages,
} from '@venio/shared/models/constants'
import { chevronLeftIcon, SVGIcon } from '@progress/kendo-svg-icons'
import { ActivatedRoute, Router } from '@angular/router'
import { InputsModule } from '@progress/kendo-angular-inputs'
import {
  BatchModel,
  CompositeLayoutFacade,
  CompositeLayoutState,
  DocumentSearchScopeModel,
  DocumentsFacade,
  Field,
  FieldFacade,
  FileTaggedResponseModel,
  FulltextFacade,
  InitialSearchResultParameter,
  MarkAsReviewedRequestModel,
  PdfViewerFacade,
  ReviewFacade,
  ReviewsetFacade,
  ReviewSetStateService,
  ReviewViewType,
  SearchFacade,
  SearchResponseData,
  SearchResponseModel,
  SearchResultFacade,
  StartupsFacade,
  ViewFacade,
  ViewModel,
} from '@venio/data-access/review'
import {
  catchError,
  combineLatest,
  debounceTime,
  distinctUntilChanged,
  EMPTY,
  filter,
  map,
  merge,
  Observable,
  of,
  Subject,
  switchMap,
  take,
  takeUntil,
  tap,
  withLatestFrom,
} from 'rxjs'
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import {
  WindowMessage,
  WindowMessageType,
  WindowMessengerService,
} from '../../../services/window.messenger.service'
import { environment } from '@venio/shared/environments'
import {
  DialogCloseResult,
  DialogRef,
  DialogService,
  DialogsModule,
} from '@progress/kendo-angular-dialog'
import {
  AppIdentitiesTypes,
  IframeMessengerService,
  MessageContent,
  MessageType,
  WINDOW,
} from '@venio/data-access/iframe-messenger'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'
import { LocalStorage } from '@venio/shared/storage'
import {
  DocumentTagFacade,
  ReviewPanelViewState,
  ReviewPanelFacade,
  ReviewPanelModel,
  ReviewPanelSelectedDocumentModel,
  UtilityPanel,
  UtilityPanelFacade,
  TagGroupInfo,
} from '@venio/data-access/document-utility'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { SortableModule } from '@progress/kendo-angular-sortable'
import { TreeListModule } from '@progress/kendo-angular-treelist'
import {
  ConfirmationDialogComponent,
  VenioNotificationService,
} from '@venio/feature/notification'
import { DocumentUtilityPanelModule } from '../document-utility-panel/document-utility-panel.module'
import { UtilityPanelStoreUpdateService } from '../utility-services/utility-panel-store-update'
import { ShortcutKeyDictionaryComponent } from '@venio/ui/shortcut-key-dictionary'
import { LayoutMenuComponent } from './layout-menu/layout-menu.component'
import { WindowManagerService } from '../../../services/window.manager.service'
import { toSignal } from '@angular/core/rxjs-interop'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { ConfirmationDialogService } from '../../../services/confirmation-dialog-service'
import { HttpErrorResponse } from '@angular/common/http'

@Component({
  selector: 'venio-document-utility',
  templateUrl: './document-utility.component.html',
  styleUrls: ['./document-utility.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    SvgLoaderDirective,
    ButtonsModule,
    InputsModule,
    LayoutModule,
    ReactiveFormsModule,
    DocumentUtilityPanelModule,
    TooltipsModule,
    DialogsModule,
    IndicatorsModule,
    DropDownsModule,
    FormsModule,
    PanelBarModule,
    SortableModule,
    ExpansionPanelModule,
    TreeListModule,
    ShortcutKeyDictionaryComponent,
    LayoutMenuComponent,
  ],
  providers: [DocumentsFacade, ReviewPanelFacade],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentUtilityComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  public utilityPanelContainerComponent: Promise<Type<unknown>>

  public projectInfoComp: Promise<Type<unknown>>

  public chevronLeftIcon: SVGIcon = chevronLeftIcon

  public svgIconForPageControls = [
    {
      actionType: PageControlActionType.FIRST_PAGE,
      actionText: PageControlActionType.FIRST_DOCUMENT_TEXT,
      iconPath: 'assets/svg/icon-pagecontrol-first-page.svg',
    },
    {
      actionType: PageControlActionType.PREV_PAGE,
      actionText: PageControlActionType.PREV_DOCUMENT_TEXT,
      iconPath: 'assets/svg/icon-pagecontrol-prev-page.svg',
    },
    {
      actionType: PageControlActionType.NEXT_PAGE,
      actionText: PageControlActionType.NEXT_DOCUMENT_TEXT,
      iconPath: 'assets/svg/icon-pagecontrol-next-page.svg',
    },
    {
      actionType: PageControlActionType.LAST_PAGE,
      actionText: PageControlActionType.LAST_DOCUMENT_TEXT,
      iconPath: 'assets/svg/icon-pagecontrol-last-page.svg',
    },
  ]

  private toDestroy$: Subject<void> = new Subject<void>()

  public currentFileId = -1

  public primaryFileId: number

  public isDocumentExistsInSearchScope: boolean

  public currentDocumentSearchScope: DocumentSearchScopeModel

  private documentIds: number[]

  public totalHitCount$: Observable<number> =
    this.searchFacade.getTotalHitCount$

  public seqNoCtrl: FormControl = new FormControl()

  public viewerComponent: Promise<Type<unknown>>

  private isViewerLoaded: boolean

  public viewerWindow: Window

  public tagsWindow: Window

  public reviewWindow: Window

  public showViewer = true

  public showTags = true

  private permittedFields: Field[]

  private currentFileSequenceNo: number

  private get applicationUrl(): string {
    if (isPlatformBrowser(this.platformId)) {
      const url = new URL(this.location.path(), window.location.href)
      return `${url.protocol}//${url.host}${environment.deployUrl}`
    }
  }

  public isReviewPanelPopoutLoding = signal(false)

  private currentDocument: number

  private userPageControlAction: PageControlActionType

  private selectedDocuments: number[]

  private isBulkDocument: boolean

  private searchResponse: SearchResponseModel

  private isBatchSelected: boolean

  private searchResults: SearchResponseData[]

  private documentPaging: {
    pageNumber: number
    resetSelectionItem: string
    paginatedDocumentIndex?: number
    pageSize?: number
  }

  private initialSearchParams: InitialSearchResultParameter

  private currentDocumentPaging: { pageNumber: number; pageSize: number }

  private userDefaultView: ViewModel

  private currentDocumentTablePage: number

  private utilityPanelUIState: UtilityPanel

  private reviewPanelUIState: ReviewPanelModel

  private selectedTagGroup: Signal<TagGroupInfo> = computed(() => ({
    title: this.reviewPanelViewState.tagGroupName(),
    actionType: this.reviewPanelViewState.selectedTagGroup(),
  }))

  private reviewViewType: ReviewViewType

  private selectReviewPanelDocument: ReviewPanelSelectedDocumentModel

  private confirmationDialogRef: DialogRef

  public projectInfoInjector: Injector

  public get isTagPanelPopout(): boolean {
    return LocalStorage.get<boolean>('isTagPanelPopout')
  }

  public get isViewerPanelPopout(): boolean {
    return LocalStorage.get<boolean>('isViewerPanelPopout')
  }

  public get isUtilityPanelDataSynced(): boolean {
    return LocalStorage.get<boolean>('isUtilityPanelDataSynced')
  }

  public get isReviewPanelPopout(): boolean {
    return LocalStorage.get<boolean>('isReviewPanelPopout')
  }

  public loadComponent: WritableSignal<boolean> = signal(true)
  private layoutFacade: CompositeLayoutFacade = inject(CompositeLayoutFacade)
  private layoutState: CompositeLayoutState = inject(CompositeLayoutState)
  private confirmationDialogService = inject(ConfirmationDialogService)

  private reviewsetFacade: ReviewsetFacade = inject(ReviewsetFacade)

  private notificationService = inject(VenioNotificationService)

  public reviewSetState = inject(ReviewSetStateService)
  private currentFileIdEmitter = new Subject<number>()
  private reviewedFileIdsEmitter = new Subject<number[]>()

  public isDocumentReviewed = toSignal(
    this.currentFileIdEmitter.pipe(
      switchMap((fileId) => {
        return this.searchResultFacade.getIsDocumentReviewedByFileId(fileId)
      }),
      takeUntil(this.toDestroy$)
    )
  )

  public isReviewedIcon = computed(() => {
    return this.isDocumentReviewed()
      ? 'assets/svg/icon-document-reviewed.svg'
      : 'assets/svg/icon-document-unreviewed.svg'
  })

  public reviewedTooltip = computed(() => {
    return this.isDocumentReviewed() ? 'Reviewed' : 'Mark as Reviewed'
  })

  public batchCheckInIcon = computed(() => {
    const batchInfo = this.reviewSetState.reviewsetBatchInfo()
    if (!batchInfo) return 'assets/svg/icon-batch-check-in-disable.svg'
    return batchInfo.remainingFiles > 0
      ? 'assets/svg/icon-batch-check-in-disable.svg'
      : 'assets/svg/icon-batch-check-in.svg'
  })

  public selectedDocumentsMetadata = toSignal(
    this.reviewedFileIdsEmitter.pipe(
      switchMap((fileIds) => {
        return this.searchResultFacade.getSearchResultEntityByFileIds(fileIds)
      }),
      takeUntil(this.toDestroy$)
    )
  )

  constructor(
    private router: Router,
    private viewFacade: ViewFacade,
    private activatedRoute: ActivatedRoute,
    private documentFacade: DocumentsFacade,
    private searchResultFacade: SearchResultFacade,
    private searchFacade: SearchFacade,
    private startupsFacade: StartupsFacade,
    private reviewPanelFacade: ReviewPanelFacade,
    private fieldFacade: FieldFacade,
    private utilityPanelFacade: UtilityPanelFacade,
    private pdfViewerFacade: PdfViewerFacade,
    private reviewPanelViewState: ReviewPanelViewState,
    private cdr: ChangeDetectorRef,
    private messengerService: WindowMessengerService,
    private windowManager: WindowManagerService,
    private iframeMessengerService: IframeMessengerService,
    private utilityPanelStoreUpdateService: UtilityPanelStoreUpdateService,
    private dialogService: DialogService,
    private location: Location,
    @Inject(PLATFORM_ID) private platformId: object,
    @Inject(WINDOW)
    private windowRef: Window,
    private documentTagsFacade: DocumentTagFacade,
    private fulltextFacade: FulltextFacade,
    private vcr: ViewContainerRef,
    private reviewFacade: ReviewFacade,
    private injector: Injector
  ) {
    this.#handleWindowClosePostMessage()
  }

  public ngOnDestroy(): void {
    this.#resetTagState()
    this.#resetUtilityPanelState()
    this.#resetReviewPanelState()
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public ngOnInit(): void {
    this.#handleLayoutLoading()
    this.#documentTagNavigation()
    this.#setMainWindowSession()
    this.#setTagPanelPopoutStatus()
    this.#setViewerPanelPopoutStatus()
    this.#utilityPanelActionEventFromPopoutWindow()
    this.#viewerPanelActionEventFromPopoutWindow()
    this.#selectSelectedDocuments()
    this.#handleUtilityPanelFullScreen()
    this.#handlePanelDataAfterLoad()
    this.#handleReviewPanelDataAfterLoad()
    this.#handleNavigateToAnotherProject()
    this.#getDocumentFileIds()
    this.#handleCurrentDocumentChange()
    this.#handleSeqNoChange()
    this.#handleMessageFromMessenger()
    this.#handleDocumentExistsInSearchScope()
    this.#refreshDocumentHistory()
    this.#loadProjectInfoComp()
    this.#onViewerContainerLoaded()
    this.handleMarkAsReviewed()
    this.#exitViewer()
  }

  #handleLayoutLoading(): void {
    this.layoutFacade.notifyLoadLayout
      .pipe(takeUntil(this.toDestroy$))
      .subscribe(() => {
        this.#resetUtilityPanel()
        this.loadComponent.set(false)
        // Notify the popout window about layout changes
        if (this.isTagPanelPopout)
          this.#sendDataToPopupWindow(WindowMessageType.VIEW_PANEL)
        setTimeout(() => {
          this.loadComponent.set(true)
        })
      })
  }

  #exitViewer(): void {
    this.searchFacade.exitViewer$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe(() => {
        this.exitViewer()
      })
  }

  /* Manage document tag navigation. */
  #documentTagNavigation(): void {
    this.documentFacade.getDocumentNavigation
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((action: PageControlActionType) => {
        this.documentNavigation(action)
      })
  }

  #onViewerContainerLoaded(): void {
    this.documentFacade.onViewerContainerLoaded
      .pipe(takeUntil(this.toDestroy$))
      .subscribe(() => {
        this.documentFacade.loadViewerContainer = this.currentFileId
      })
  }

  public ngAfterViewInit(): void {
    this.#loadLazyComponents()
    this.#handleQueryParams()
  }

  #handleQueryParams(): void {
    this.activatedRoute.queryParams
      .pipe(
        switchMap((params) => {
          if (params.reviewSetId) {
            this.reviewSetState.reviewSetId.set(params.reviewSetId)
            return this.reviewsetFacade.fetchReviewSetBasicInfo$(
              params.projectId,
              params.reviewSetId
            )
          } else {
            return of({
              message: '',
              status: 'Success',
              data: { projectId: params.projectId, reviewSetId: -1 },
            } as ResponseModel)
          }
        }),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response: ResponseModel) => {
        if (response?.data?.reviewSetId > 0) {
          this.reviewSetState.reviewSetBasicInfo.set(response.data)
        }
      })
  }

  #loadProjectInfoComp(): void {
    this.projectInfoComp = import(
      '../../layout/layout-toolbar/project-info/project-info.component'
    ).then(({ ProjectInfoComponent }) => {
      this.createInjector('review') // Passing the string 'review'
      return ProjectInfoComponent
    })
  }

  private createInjector(selectorViewValue: string): void {
    this.projectInfoInjector = Injector.create({
      providers: [{ provide: 'selectorView', useValue: selectorViewValue }],
      parent: this.injector,
    })
  }

  #resetUpdateDocumentHistoryPage(): void {
    this.reviewPanelFacade.resetReviewPanelState(['updateDocumentHistoryPage'])
  }

  #resetTagState(): void {
    this.documentTagsFacade.resetDocumentTagState([
      'documentTags',
      'searchDocumentTags',
      'filterDocumentTags',
    ])
  }

  #resetUtilityPanelState(): void {
    this.utilityPanelFacade.resetUtilityPanelState([
      'isUtilityPanelInFullScreen',
      'isViewerInFullScreen',
    ])
  }

  #resetReviewPanelState(): void {
    this.reviewPanelFacade.resetReviewPanelState(['similarDocuments'])
  }

  #resetUtilityPanel(): void {
    this.utilityPanelFacade.resetUtilityPanelState([
      'expandedStatus',
      'visibilityStatus',
      'panelSortOrder',
    ])
  }

  #setMainWindowSession(): void {
    this.windowRef.name = 'mainWindow'
  }

  #loadLazyComponents(): void {
    this.cdr.markForCheck()
    this.utilityPanelContainerComponent = import(
      '../document-utility-panel/document-utility-panel-container/document-utility-panel-container.component'
    ).then(
      ({ DocumentUtilityPanelContainerComponent }) =>
        DocumentUtilityPanelContainerComponent
    )
  }

  #selectSelectedDocuments(): void {
    combineLatest([
      this.documentFacade.getSelectedDocuments$,
      this.documentFacade.getCurrentDocument$,
      this.documentFacade.isBulkDocument$,
      this.searchFacade.getSearchResponse$,
      this.documentFacade.getIsBatchSelected$,
      this.searchResultFacade.getAllSearchResults,
      this.documentFacade.getCurrentDocumentTablePage$,
      this.searchResultFacade.getCurrentDocumentTablePaging$,
      this.searchFacade.getSearchInitialParameters$,
      this.viewFacade.selectUserDefaultView$,
      this.utilityPanelFacade.selectUtilityPanel$,
      this.reviewPanelFacade.selectReviewPanelData$,
      this.fieldFacade.selectAllPermittedFieldsOfCurrentUser$,
      this.reviewFacade.getReviewViewType$,
    ])
      .pipe(takeUntil(this.toDestroy$))
      .subscribe(
        ([
          selectedDocuments,
          currentDocument,
          isBulkDocument,
          searchResponse,
          isBatch,
          searchResults,
          currentDocumentTablePage,
          documentPaging,
          initialSearchParams,
          userDefaultView,
          utilityPanel,
          reviewPanelData,
          permittedFields,
          reviewViewType,
        ]) => {
          this.selectedDocuments = selectedDocuments
          this.currentDocument = currentDocument
          this.isBulkDocument = isBulkDocument
          this.searchResponse = searchResponse
          this.isBatchSelected = isBatch
          this.searchResults = searchResults
          this.currentDocumentTablePage = currentDocumentTablePage
          this.documentPaging = documentPaging
          this.initialSearchParams = initialSearchParams
          this.userDefaultView = userDefaultView
          this.utilityPanelUIState = utilityPanel
          this.reviewPanelUIState = reviewPanelData
          this.permittedFields = permittedFields
          this.reviewViewType = reviewViewType
        }
      )
  }

  #handleMessageFromMessenger(): void {
    this.messengerService.messageReceived
      .pipe(
        filter((message: WindowMessage) => !!message?.payload?.type),
        takeUntil(this.toDestroy$)
      )
      .subscribe((message: WindowMessage) => {
        this.cdr.markForCheck()
        switch (message.payload.type) {
          case WindowMessageType.VIEWER_CLOSED:
            this.#resetOpenerWindows()
            this.utilityPanelFacade.setIsViewerInFullScreen(false)
            this.#setViewerPanelPopoutStatus()
            this.#showPanels()
            break
          case WindowMessageType.POPUP_VIEWER_READY:
            this.#sendViewerDataToPopupWindow()
            break
        }
      })
  }

  #sendViewerDataToPopupWindow(): void {
    combineLatest([
      this.searchFacade.getSearchResponse$,
      this.documentFacade.getCurrentDocument$,
      this.startupsFacade.getUserRights$,
      this.fulltextFacade.fetchMetadata$,
    ])
      .pipe(debounceTime(1000), take(1))
      .subscribe(([searchResponse, fileId, userRights, fulltextFields]) => {
        this.messengerService.sendMessage(
          {
            payload: {
              type: WindowMessageType.VIEW_DOCUMENT,
              content: {
                fileId,
                searchResponse,
                userRights,
                fulltextFields,
                selectedlayout: this.layoutState.userSelectedLayout(),
              },
            },
          },
          this.viewerWindow ? this.viewerWindow : this.reviewWindow
        )
        this.#navigateToDocumentsPage()
      })
  }

  #handleCurrentDocumentChange(): void {
    merge(
      this.documentFacade.getCurrentDocument$.pipe(
        map((value) => ({ emittedFrom: 'DOCUMENT_CHANGED', value }))
      ),
      this.reviewPanelFacade.fetchSelectedParentChildDocument$.pipe(
        map((value) => ({ emittedFrom: 'REVIEW_PANEL_VIEW', value }))
      )
    )
      .pipe(
        filter((val) => !!val.value),
        switchMap((emission: any) => {
          if (emission.emittedFrom === 'REVIEW_PANEL_VIEW') {
            return this.#handleDocumentChangeFromReviewPanels(emission?.value)
          }
          this.isDocumentExistsInSearchScope = true
          this.primaryFileId = emission.value
          this.setCurrentFileId(emission.value)
          return this.searchResultFacade.getSeqNoByFileId(this.currentFileId)
        }),
        tap((seqNo) => {
          const sequenceNo = seqNo > 0 ? seqNo : null
          this.seqNoCtrl.setValue(sequenceNo, { emitEvent: false })
          this.currentFileSequenceNo = sequenceNo
        }),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.cdr.markForCheck()
        this.documentFacade.setSelectedDocuments([this.currentFileId])
        this.reviewPanelFacade.resetReviewPanelState([
          'selectedReviewPanelDocument',
        ])
        this.#loadViewer()
        // Notify the popup windows (viewer/utility panel) about the file id change
        this.#notifyFileIDChangedToPopupWindow(this.viewerWindow)
        this.#notifyFileIDChangedToPopupWindow(this.tagsWindow)
        // Notify to parent window about the file id change
        this.#notifyFileIDChangedToPopupWindow(window.opener)
      })
  }

  private setCurrentFileId(fileId: number): void {
    this.currentFileId = fileId
    this.currentFileIdEmitter.next(this.currentFileId)
  }

  /**
   * Sends a message to a popup window to notify it about a change in file ID.
   * @param popoutWindow The popup window to send the message to.
   */
  #notifyFileIDChangedToPopupWindow(popoutWindow: Window): void {
    if (popoutWindow) {
      this.messengerService.sendMessage(
        {
          payload: {
            type: WindowMessageType.FILEID_CHANGED,
            content: {
              fileId: this.currentFileId,
              primaryFileId: this.primaryFileId,
              currentFileSequenceNo: this.currentFileSequenceNo,
              userPageControlAction: this.userPageControlAction,
              currentDocumentSearchScope: this.currentDocumentSearchScope,
              currentDocumentTablePage: this.currentDocumentTablePage,
            },
          },
        },
        popoutWindow
      )
    }
  }

  #handleDocumentChangeFromReviewPanels(
    value: ReviewPanelSelectedDocumentModel
  ): Observable<number> {
    // If the user has already navigated to an out-of-scope document and then selects another out-of-scope document,
    // we need to retain the primaryFileId. Without it, navigating to the next document using the Next button in the UI won't work.
    const currentFileId = this.isDocumentExistsInSearchScope
      ? value.currentfileId
      : this.primaryFileId
    this.currentDocumentSearchScope = {
      ...value,
      primaryFileId: currentFileId,
    }
    this.searchResultFacade.setDocumentExistsInSearchScope(
      this.currentDocumentSearchScope
    )
    if (!value?.isDocumentExistsInSearchScope) {
      const content = ParentChildMessages.FILE_NOT_IN_SCOPE(value.documentNo)
      return this.#showConfirmationMessage(
        content,
        value.documentNo,
        currentFileId
      )
    }
    this.isDocumentExistsInSearchScope = true
    this.setCurrentFileId(value.documentNo)
    this.primaryFileId = value.currentfileId
    return this.searchResultFacade.getSeqNoByFileId(this.currentFileId)
  }

  /**
   * Determines if the current document is out of scope.
   * This method is primarily used when the review pop-out panel is closed and the application redirects to the document viewer.
   * It helps assess whether the current document falls within the search scope.
   * @returns {void} This method does not return any value.
   */
  #handleDocumentExistsInSearchScope(): void {
    this.searchResultFacade.getDocumentExistsInSearchScope$
      .pipe(
        filter((currentDocumentSearchScope) =>
          Boolean(currentDocumentSearchScope)
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe((currentDocumentSearchScope) => {
        this.currentDocumentSearchScope = currentDocumentSearchScope
        this.isDocumentExistsInSearchScope = true
        if (
          this.currentFileId === this.currentDocumentSearchScope?.documentNo
        ) {
          const { isDocumentExistsInSearchScope, primaryFileId } =
            this.currentDocumentSearchScope
          this.isDocumentExistsInSearchScope = isDocumentExistsInSearchScope
          this.primaryFileId = primaryFileId
        }
        this.cdr.markForCheck()
      })
  }

  /**
   * This function shows a notification message.
   * @param {string} content The content of the notification message.
   * @returns {Observable of number}
   */
  #showConfirmationMessage(
    content = '',
    fileId: number,
    primaryFileId: number
  ): Observable<number> {
    this.confirmationDialogRef = this.dialogService.open({
      content: ConfirmationDialogComponent,
      appendTo: this.vcr,
      cssClass: 'v-confirmation-dialog v-dialog-warning',
      width: '35rem',
    })

    // Set the dialog input
    this.#setDialogInput(this.confirmationDialogRef.content.instance, content)

    return this.confirmationDialogRef.result.pipe(
      switchMap((result) => {
        if (result instanceof DialogCloseResult) {
          return EMPTY
        }
        this.isDocumentExistsInSearchScope = false
        this.setCurrentFileId(fileId)
        this.primaryFileId = primaryFileId
        //-2 value is selected as the seq no which prevents it from navigating to the document
        return of(-2)
      })
    )
  }

  #handleSeqNoChange(): void {
    this.seqNoCtrl.valueChanges
      .pipe(
        debounceTime(600),
        distinctUntilChanged(),
        filter((seqNo) => !!seqNo),
        withLatestFrom(this.reviewFacade.getReviewViewType$),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([seqNo, viewType]) => {
        //reset the userPageControlAction if user enter the seq no
        this.userPageControlAction = undefined
        const handler = (seqNo: number, viewType: ReviewViewType): void => {
          if (viewType === ReviewViewType.EmailThread) {
            this.documentFacade.navigateEmailThreadByNumber(+seqNo)
          } else {
            this.documentFacade.navigateDocumentByNumber(+seqNo)
          }
        }
        this.pdfViewerFacade.executeWithAnnotationCheck(
          handler,
          +seqNo,
          viewType
        )
      })
  }

  #getDocumentFileIds(): void {
    this.searchResultFacade.getSearchResultFileIds
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((fileIds) => {
        this.documentIds = fileIds
      })
  }

  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  private get docShareToken(): number {
    return this.activatedRoute.snapshot.queryParams['docShareToken']
  }

  private get moduleData(): number {
    return this.activatedRoute.snapshot.queryParams['moduleData']
  }

  #refreshDocumentHistory(): void {
    this.reviewPanelFacade.selectUpdateDocumentHistoryPage$
      .pipe(
        filter(
          (updateDocumentHistory) =>
            Boolean(updateDocumentHistory) && this.isTagPanelPopout
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.notifyRedactionUpdateToPopoutTagWindow()
        this.#resetUpdateDocumentHistoryPage()
      })
  }

  /**
   * Sends a redaction notification to the tag popout window.
   * @returns {void} This method does not return anything.
   */
  public notifyRedactionUpdateToPopoutTagWindow(): void {
    this.iframeMessengerService.sendMessage({
      type: 'MICRO_APP_DATA_CHANGE',
      payload: {
        type: MessageType.REDACTION_UPDATE,
        content: {
          isRefreshPage: true,
        },
      },
      eventTriggeredFor: this.tagsWindow,
      eventTriggeredBy: AppIdentitiesTypes.VENIO_NEXT,
      iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
    })
  }

  public exitViewer(): void {
    const handler = (): void => {
      this.router
        .navigate(['/documents'], {
          queryParams: {
            projectId: this.projectId,
            docShareToken: this.docShareToken,
            reviewSetId: this.reviewSetState.reviewSetId(),
            moduleData: this.moduleData,
          },
        })
        .then(() => {
          // We need to close all the popout windows when we exit the viewer
          this.#closeAllWindows()
          // Reset the selected documents

          if (!this.isReviewPanelPopout) {
            // Reset selected documents only if the review panel is not in pop-out mode.
            // If the review panel is in pop-out mode, keep the selected documents' checkboxes checked in the document table.
            this.documentFacade.setSelectedDocuments([])
          }

          this.documentFacade.setIsBatchSelection(false)
          this.searchResultFacade.fetchDocumentTablePage()
          // After navigating to the document page, fetch the user's default view
          this.viewFacade.fetchUserDefaultView(this.projectId)
        })
    }
    this.pdfViewerFacade.executeWithAnnotationCheck(handler)
  }

  #closeAllWindows(): void {
    if (this.viewerWindow) {
      this.viewerWindow.close()
      this.viewerWindow = null
    }
    if (this.tagsWindow) {
      this.tagsWindow.close()
      this.tagsWindow = null
    }

    this.showViewer = true
    this.showTags = true
  }

  /**
   * Listens for utility panel action events originating from a popout window.
   * When an event is received, it processes the action accordingly.
   * @returns {void} This method does not return anything.
   */
  #utilityPanelActionEventFromPopoutWindow(): void {
    this.iframeMessengerService.messageReceived
      .pipe(
        filter(
          (message) =>
            this.isTagPanelPopout &&
            message.type === 'MICRO_APP_DATA_CHANGE' &&
            (message.payload as MessageContent).type ===
              MessageType.WINDOW_CHANGE &&
            Boolean(message.payload?.['content']?.['pageControlActionType']) &&
            message.eventTriggeredBy ===
              AppIdentitiesTypes.UTILITY_PANEL_ACTION &&
            message.eventTriggeredFor === 'ALL_WINDOW'
        ),
        map((message) => message.payload as MessageContent),
        takeUntil(this.toDestroy$)
      )
      .subscribe((action) => {
        this.#handleUtilityPanelActionEvents(action)
      })
  }

  /**
   * Listens for viewer panel action events originating from a popout window.
   * When an event is received, it processes the action accordingly.
   * @returns {void} This method does not return anything.
   */
  #viewerPanelActionEventFromPopoutWindow(): void {
    this.iframeMessengerService.messageReceived
      .pipe(
        filter(
          (message) =>
            this.isViewerLoaded &&
            message.type === 'MICRO_APP_DATA_CHANGE' &&
            (message.payload as MessageContent).type ===
              MessageType.WINDOW_CHANGE &&
            Boolean(message.payload?.['content']?.['pageControlActionType']) &&
            message.eventTriggeredBy === AppIdentitiesTypes.VIEWER_PANEL &&
            message.eventTriggeredFor === 'ALL_WINDOW'
        ),
        map((message) => message.payload as MessageContent),
        takeUntil(this.toDestroy$)
      )
      .subscribe((action) => {
        this.#handleUtilityPanelActionEvents(action)
      })
  }

  // Handles utility panel action events
  #handleUtilityPanelActionEvents(action): void {
    // Check if the action is for navigating to the next page
    const isNext =
      action.content.pageControlActionType === PageControlActionType.NEXT_PAGE

    // Check if the action is for navigating to the previous page
    const isBack =
      action.content.pageControlActionType === PageControlActionType.PREV_PAGE

    // Check if the action is for parent-child document navigation

    const isDocumentNavigate =
      action.content.pageControlActionType ===
        PageControlActionType.PARENT_CHILD_DOCUMENT ||
      PageControlActionType.DUPLICATE_DOCUMENT ||
      PageControlActionType.SIMILAR_DOCUMENT ||
      PageControlActionType.EMAIL_THREAD ||
      PageControlActionType.LINK_DOCUMENT ||
      PageControlActionType.NEAR_DUPLICATE

    if (isNext) {
      this.documentNavigation(PageControlActionType.NEXT_PAGE)
    }
    if (isBack) {
      this.documentNavigation(PageControlActionType.PREV_PAGE)
    }
    if (isDocumentNavigate) {
      this.selectReviewPanelDocument = action.content.selectReviewPanelDocument

      this.reviewPanelFacade.setSelectedReviewPanelDocument(
        this.selectReviewPanelDocument
      )
    }
  }

  /**
   * Handles the full screen event for the utility panel.
   * @returns {void} This method does not return anything.
   */
  #handleUtilityPanelFullScreen(): void {
    this.utilityPanelFacade.selectUtilityPanelInFullScreen$
      .pipe(
        distinctUntilChanged(),
        filter((isUtilityPanelFullScreen) => Boolean(isUtilityPanelFullScreen)),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.#tagsFullScreenHOpener()
      })
  }

  /**
   * Sends data to the review popup window once it's ready,
   * and handles closure of the review panel.
   * @returns {void} This method does not return anything.
   */
  #handleReviewPanelDataAfterLoad(): void {
    this.messengerService.messageReceived
      .pipe(
        filter(
          (message: WindowMessage) =>
            Boolean(message?.payload?.type) &&
            this.isReviewPanelPopout &&
            (message.payload.type ===
              WindowMessageType.POPUP_REVIEW_PANEL_READY ||
              message.payload.type === WindowMessageType.REVIEW_PANEL_CLOSED)
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe((message: WindowMessage) => {
        this.cdr.markForCheck()
        switch (message?.payload?.type) {
          case WindowMessageType.REVIEW_PANEL_CLOSED:
            // If user close the review panel before navigate to document table
            this.#reviewPanelWindowUnloadHandler(message.payload)
            break
          case WindowMessageType.POPUP_REVIEW_PANEL_READY:
            this.#notifyNavigatedFromViewer()
            this.#sendDataToPopupWindow(WindowMessageType.REVIEW_PANEL)
            break
        }
      })
  }

  /**
   * Sends data to the utility popup window once it's ready,
   * and handles closure of the utility panel.
   * @returns {void} This method does not return anything.
   */
  #handlePanelDataAfterLoad(): void {
    this.messengerService.messageReceived
      .pipe(
        filter(
          (message: WindowMessage) =>
            Boolean(message?.payload?.type) &&
            this.isTagPanelPopout &&
            (message.payload.type ===
              WindowMessageType.POPUP_UTILITY_PANEL_READY ||
              message.payload.type === WindowMessageType.UTILITY_PANEL_CLOSED)
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe((message: WindowMessage) => {
        this.cdr.markForCheck()
        switch (message.payload.type) {
          case WindowMessageType.UTILITY_PANEL_CLOSED:
            this.#utilityPanelWindowUnloadHandler(message.payload)
            break
          case WindowMessageType.POPUP_UTILITY_PANEL_READY:
            this.#sendDataToPopupWindow(WindowMessageType.VIEW_PANEL)
            break
        }
      })
  }

  #loadViewer(): void {
    this.documentFacade.loadViewerContainer = this.currentFileId
    if (!this.isViewerLoaded) {
      this.viewerComponent = import(
        '../../viewers/viewer-container/viewer-container.component'
      ).then(({ ViewerContainerComponent }) => ViewerContainerComponent)
      this.isViewerLoaded = true
    }
  }

  #reviewFullScreenHOpener(): void {
    let url = `${this.applicationUrl}/#/review?projectId=${this.projectId}`
    if (this.reviewSetState.isBatchReview()) {
      url += `&reviewSetId=${this.reviewSetState.reviewSetId()}`
    }
    if (this.reviewWindow && !this.reviewWindow.closed) {
      this.reviewWindow.focus()
    } else {
      this.reviewWindow = this.windowRef.open(
        url,
        '_blank',
        'width=1000,height=800'
      )
      this.#setReivewPanelWindowState()
      this.#setReviewPanelPopoutStatus()
    }
  }

  #viewerFullScreenHOpener(): void {
    const url = `${this.applicationUrl}/#/viewer?projectId=${this.projectId}`
    if (this.viewerWindow && !this.viewerWindow.closed) {
      this.viewerWindow.focus()
    } else {
      this.viewerWindow = this.windowRef.open(
        url,
        '_blank',
        'width=1000,height=800'
      )
      this.showViewer = false
      this.utilityPanelFacade.setIsViewerInFullScreen(true)
      this.#setViewerPanelPopoutStatus()
    }
  }

  /** Handles the event when the review panel is closed.
   * @param {WindowMessage} content The content of the message.
   * @returns {void} This method does not return anything.
   */
  #reviewPanelWindowUnloadHandler(content): void {
    LocalStorage.set('isReviewPanelPopout', false)
    LocalStorage.set('isUtilityPanelDataSynced', false)
    this.isReviewPanelPopoutLoding.set(false)
    this.windowManager.removeWindow(AppIdentitiesTypes.REVIEW_PANEL)
    this.utilityPanelStoreUpdateService.updateStoreDataFromParentWindow(content)
    this.#loadLazyComponents()
    this.#resetOpenerWindows()
    this.#showPanels()
  }

  /** Handles the event when the utility panel is closed.
   * @param {WindowMessage} content The content of the message.
   * @returns {void} This method does not return anything.
   */
  #utilityPanelWindowUnloadHandler(content): void {
    if (!this.tagsWindow) return
    LocalStorage.set('isTagPanelPopout', false)
    LocalStorage.set('isUtilityPanelDataSynced', false)
    this.utilityPanelStoreUpdateService.updateStoreDataFromParentWindow(content)
    this.utilityPanelFacade.setIsUtilityPanelInFullScreen(false)
    this.#loadLazyComponents()
    this.#resetOpenerWindows()
    this.#showPanels()
    this.cdr.markForCheck()
  }

  #setTagPanelPopoutStatus(): void {
    if (!this.tagsWindow) {
      LocalStorage.set('isTagPanelPopout', false)
      LocalStorage.set('isUtilityPanelDataSynced', false)
      return
    }
    LocalStorage.set('isTagPanelPopout', !this.tagsWindow.closed)
  }

  #setViewerPanelPopoutStatus(): void {
    if (!this.viewerWindow) {
      LocalStorage.set('isViewerPanelPopout', false)
      return
    }
    LocalStorage.set('isViewerPanelPopout', !this.viewerWindow.closed)
  }

  #setReviewPanelPopoutStatus(): void {
    if (!this.reviewWindow) {
      LocalStorage.set('isReviewPanelPopout', false)
      return
    }
    LocalStorage.set('isReviewPanelPopout', !this.reviewWindow.closed)
  }

  #setDocumentPaging(): void {
    if (this.documentPaging)
      this.currentDocumentPaging = {
        pageNumber:
          this.documentPaging?.pageNumber || this.currentDocumentTablePage,
        pageSize:
          this.documentPaging?.pageSize || this.initialSearchParams.pageSize,
      }
  }

  #setReivewPanelWindowState(): void {
    this.windowManager.registerWindow(
      AppIdentitiesTypes.REVIEW_PANEL,
      this.reviewWindow
    )
    this.isReviewPanelPopoutLoding.set(true)
  }

  /**
   * Sends data to the popup window.
   * Sets document paging and sends relevant data to the popup window using the messenger service.
   * @returns {void} This method does not return anything.
   */
  #sendDataToPopupWindow(panelType: WindowMessageType): void {
    const window =
      panelType === WindowMessageType.VIEW_PANEL
        ? this.tagsWindow
        : this.reviewWindow
    this.#setDocumentPaging()
    this.messengerService.sendMessage(
      {
        payload: {
          type: panelType,
          content: {
            projectId: this.projectId,
            selectedDocuments: this.selectedDocuments,
            currentDocument: this.currentDocument,
            isBulkDocument: this.isBulkDocument,
            searchResponse: this.searchResponse,
            isBatchSelected: this.isBatchSelected,
            searchResults: this.searchResults,
            documentPaging: this.currentDocumentPaging,
            userDefaultView: this.userDefaultView,
            utilityPanelUIState: this.utilityPanelUIState,
            reviewPanelUIState: this.reviewPanelUIState,
            userSelectedLayout: this.layoutState.userSelectedLayout(),
            selectedTagGroup: this.selectedTagGroup(),
            permittedFields: this.permittedFields,
            reviewViewType: this.reviewViewType,
            reviewSetBasicInfo: this.reviewSetState.reviewSetBasicInfo(),
            reviewsetBatchInfo: this.reviewSetState.reviewsetBatchInfo(),
            batchdId: this.reviewSetState.batchId(),
          },
        },
      },
      window
    )
    LocalStorage.set('isUtilityPanelDataSynced', true)

    if (panelType === WindowMessageType.REVIEW_PANEL) {
      this.#sendViewerDataToPopupWindow()
    }
  }

  #resetOpenerWindows(): void {
    if (this.viewerWindow) {
      this.viewerWindow.close()
      this.viewerWindow = null
    }
    if (this.tagsWindow) {
      this.tagsWindow.close()
      this.tagsWindow = null
    }
    if (this.reviewWindow) {
      this.reviewWindow.close()
      this.reviewWindow = null
    }
  }

  #showPanels(): void {
    this.cdr.markForCheck()
    this.showViewer = true
    this.showTags = true
  }

  #navigateToDocumentsPage(): void {
    this.isReviewPanelPopoutLoding.set(false)
    if (this.reviewWindow && this.isReviewPanelPopout) this.exitViewer()
  }

  #notifyNavigatedFromViewer(): void {
    this.reviewPanelViewState.setIsNavigatedFromViewer(true)
  }

  #tagsFullScreenHOpener(): void {
    const url = `${this.applicationUrl}/#/utility-panel?projectId=${this.projectId}`
    if (this.tagsWindow && !this.tagsWindow.closed) {
      this.tagsWindow.focus()
    } else {
      this.tagsWindow = this.windowRef.open(
        url,
        '_blank',
        'width=1000,height=800'
      )
      this.showTags = false
      this.#setTagPanelPopoutStatus()
    }
  }

  #handleWindowClosePostMessage(): void {
    this.iframeMessengerService.messageReceived
      .pipe(
        filter(
          (message) =>
            message.type === 'MICRO_APP_DATA_CHANGE' &&
            (message.payload as MessageContent).type ===
              MessageType.ROUTE_CHANGE &&
            Boolean(message.payload?.['content']?.['closeAllPopoutWindows'])
        ),
        map((message) => message.payload as MessageContent),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.#closeAllWindows()
      })
  }

  public documentNavigation(action: PageControlActionType): void {
    this.userPageControlAction = action
    const handler = (): void => {
      switch (action) {
        case PageControlActionType.FIRST_PAGE:
          this.documentFacade.moveToFirstDocument()
          break
        case PageControlActionType.PREV_PAGE:
          this.documentFacade.moveToPreviousDocument(
            this.documentIds.filter((id) => id > 0),
            this.primaryFileId
          )
          break
        case PageControlActionType.NEXT_PAGE:
          this.documentFacade.moveToNextDocument(
            this.documentIds.filter((id) => id > 0),
            this.primaryFileId
          )
          break
        case PageControlActionType.LAST_PAGE:
          this.documentFacade.moveToLastDocument()
          break
      }
    }
    this.pdfViewerFacade.executeWithAnnotationCheck(handler)
  }

  public onFullscreenClick(uiType: 'TAGS' | 'VIEWER' | 'REVIEW'): void {
    switch (uiType) {
      case 'TAGS':
        this.#tagsFullScreenHOpener()
        break
      case 'VIEWER':
        this.#viewerFullScreenHOpener()
        break
      case 'REVIEW':
        this.#reviewFullScreenHOpener()
        break
    }
  }

  /**
   * This function sets the input for the dialog.
   * @param instance The instance of the ConfirmationDialogComponent.
   * @param content The content of the notification message.
   */

  #setDialogInput(instance: ConfirmationDialogComponent, content = ''): void {
    // Set the title of the dialog
    instance.title = 'View Document Details'
    // Set the message of the dialog
    instance.message = content
  }

  #handleNavigateToAnotherProject(): void {
    this.messengerService.messageReceived
      .pipe(
        filter(
          (message: WindowMessage) =>
            Boolean(message?.payload?.type) &&
            this.isReviewPanelPopout &&
            message.payload.type === WindowMessageType.ROUTE_CHANGED
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe((message: WindowMessage) => {
        const { projectId } = message.payload.content
        const queryParams = { ...this.activatedRoute.snapshot.queryParams }

        // Update or add new query parameters
        queryParams['projectId'] = projectId

        // Navigate to the same route with updated query parameters
        this.router
          .navigate([], {
            relativeTo: this.activatedRoute,
            queryParams: queryParams,
            queryParamsHandling: 'merge', // merge with existing query params
          })
          .then(() => {
            this.utilityPanelStoreUpdateService.resetStoreDataForRouteChanged()
            this.utilityPanelStoreUpdateService.updateStoreDataForProjectChanged(
              message.payload
            )
            this.isViewerLoaded = false
            this.#loadViewer()
          })
      })
  }
  public onMarkAsReviewed(): void {
    this.reviewsetFacade.markCurrentDocumentAsReviewedAction.next(
      this.currentFileId
    )
  }

  public handleMarkAsReviewed(): void {
    this.reviewsetFacade.markCurrentDocumentAsReviewedAction
      .pipe(
        filter(() => !this.isDocumentReviewed()),
        switchMap((fileId: number) => {
          const payload: MarkAsReviewedRequestModel = {
            fileIds: [],
            globalTempTable:
              this.searchResponse?.tempTables?.searchResultTempTable,
            isBatchSelected: false,
            unSelectedFileIds: [],
          }
          return this.reviewsetFacade
            .markAsReviewed$<ResponseModel>(
              this.projectId,
              this.reviewSetState.reviewSetId(),
              this.reviewSetState.batchId(),
              fileId,
              payload
            )
            .pipe(
              switchMap((response: ResponseModel) => {
                if (response.status === 'Success') {
                  this.reviewSetState.isBatchInfoLoading.set(true)

                  return combineLatest([
                    this.reviewsetFacade.fetchReviewSetBatchInfo$(
                      this.projectId,
                      this.reviewSetState.reviewSetId(),
                      this.reviewSetState.batchId()
                    ),
                    this.reviewsetFacade.reviewedFileStatus$(this.projectId, {
                      reviewSetId: this.reviewSetState.reviewSetId(),
                      currentPageFileIds: this.documentIds,
                      isFileTagged: false,
                    }),
                  ]).pipe(
                    switchMap(
                      ([batchResponse, statusResponse]: [
                        ResponseModel,
                        FileTaggedResponseModel
                      ]) => {
                        const batchInfo: BatchModel = batchResponse?.data
                        this.reviewSetState.reviewsetBatchInfo.set(batchInfo)

                        // Get the reviewed file IDs from the status response
                        const reviewedFileIds =
                          statusResponse?.reviewedFileIds || [fileId]
                        this.reviewedFileIdsEmitter.next(reviewedFileIds)

                        if (batchInfo.remainingFiles === 0) {
                          // Update the reviewed icons for all reviewed file IDs
                          this.updateReviewedIcons(reviewedFileIds)

                          const message =
                            'Review of all documents in batch is completed. Do you want to check in and open new batch?'
                          return this.confirmationDialogService
                            .showConfirmationDialog('Check In', message)
                            .pipe(
                              filter((confirmed) => confirmed),
                              switchMap(() => {
                                return this.handleBatchCheckIn()
                              }),
                              catchError((err: unknown) => {
                                const httpError = err as HttpErrorResponse
                                this.notificationService.showError(
                                  httpError.error.message
                                )
                                return EMPTY
                              })
                            )
                        } else return of(reviewedFileIds)
                      }
                    )
                  )
                } else return EMPTY
              })
            )
        }),
        takeUntil(this.toDestroy$)
      )
      .subscribe((result) => {
        this.reviewSetState.isBatchInfoLoading.set(false)
        if (Array.isArray(result)) {
          this.updateReviewedIcons(result)
        }
      })
  }

  private updateReviewedIcons(fileIds: number[]): void {
    if (!fileIds || fileIds.length === 0) {
      return
    }

    // Wait for the selectedDocumentsMetadata signal to be populated with updated data
    setTimeout(() => {
      const metadata = this.selectedDocumentsMetadata()
      if (!metadata || metadata.length === 0) {
        return
      }

      const updatedRows = metadata
        .filter((doc) => fileIds.includes(doc.fileId))
        .map((doc) => {
          return {
            fileId: doc.fileId,
            metadata: doc.metadata.map((item) => {
              if (item.key === '__isReviewed') {
                return { ...item, value: 'Yes' }
              }
              return item
            }),
          }
        })

      if (updatedRows.length > 0) {
        this.searchResultFacade.updateManySearchResult(updatedRows)
        this.notificationService.showSuccess(
          'Document marked as reviewed successfully'
        )
      }
    }, 300)
  }

  private handleBatchCheckIn(): Observable<any> {
    const batchInfo = this.reviewSetState.reviewsetBatchInfo()
    if (batchInfo.remainingFiles > 0) return

    return this.reviewsetFacade
      .checkInReviewBatch$(
        this.projectId,
        this.reviewSetState.reviewSetId(),
        this.reviewSetState.batchId()
      )
      .pipe(
        filter((response) => Boolean(response)),
        switchMap((checkInResponse: ResponseModel) => {
          this.notificationService.showSuccess(checkInResponse.message)
          let message =
            'Review batch is checked in. Do you want to open new batch?'
          if (checkInResponse.data.notStartedBatchCount > 0) {
            message =
              'Review batch is checked in. Do you want to open new batch?'
            return this.confirmationDialogService
              .showConfirmationDialog('Check In', message)
              .pipe(
                tap((confirmed) => {
                  if (confirmed) {
                    this.reviewsetFacade.checkoutBatchReviewSetAction.next(
                      this.reviewSetState.reviewSetId()
                    )
                    this.searchResultFacade.resetSearchResults()
                    this.exitViewer()
                    return EMPTY
                  } else {
                    this.redirectToLaunchpad()
                  }
                }),
                switchMap(() => this.searchResultFacade.getSearchResultFileIds),
                filter((result) => result && result.length > 0)
              )
          } else {
            message =
              'Review batch is checked in and no remaining batches to checkout'
            this.redirectToLaunchpad()
            return EMPTY
          }
        })
      )
  }

  public redirectToLaunchpad(): void {
    this.iframeMessengerService.sendMessage({
      type: 'MICRO_APP_DATA_CHANGE',
      payload: {
        type: MessageType.ROUTE_CHANGE,
        content: {
          isRefreshPage: false,
          isBatchReview: true,
        },
      },
      eventTriggeredFor: 'PARENT_WINDOW',
      eventTriggeredBy: AppIdentitiesTypes.VENIO_NEXT,
      iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
    })
  }

  public checkInBatch(): void {
    this.handleBatchCheckIn()
      .pipe((take(1), takeUntil(this.toDestroy$)))
      .subscribe(() => {
        this.documentFacade.moveToFirstDocument()
      })
  }
}
