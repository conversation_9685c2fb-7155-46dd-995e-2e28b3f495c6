import { HttpClient } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { environment } from '@venio/shared/environments'
import { Observable } from 'rxjs'
import {
  MarkAsReviewedRequestModel,
  ReviewStatusRequestModel,
} from '../../models/interfaces'

@Injectable({
  providedIn: 'root',
})
export class ReviewSetService {
  private get _apiUrl(): string {
    return environment.apiUrl.replace(/\/+$/, '')
  }

  constructor(private http: HttpClient) {}

  public fetchReviewSetBasicInfo$<T>(
    projectId: number,
    reviewSetId: number
  ): Observable<T> {
    return this.http.get<T>(
      this._apiUrl +
        '/project/' +
        projectId +
        '/reviewset/' +
        reviewSetId +
        '/basic-info'
    )
  }

  public markAsReviewed$<T>(
    projectId: number,
    reviewSetId: number,
    batchId: number,
    fileId: number,
    requestModel: MarkAsReviewedRequestModel
  ): Observable<T> {
    return this.http.post<T>(
      this._apiUrl +
        '/project/' +
        projectId +
        '/reviewset/' +
        reviewSetId +
        '/batch/' +
        batchId +
        '/document/' +
        fileId +
        '/reviewed',
      requestModel
    )
  }

  public markAsReviewedBulk$<T>(
    projectId: number,
    reviewSetId: number,
    batchId: number,
    requestModel: MarkAsReviewedRequestModel
  ): Observable<T> {
    return this.http.post<T>(
      this._apiUrl +
        '/project/' +
        projectId +
        '/reviewset/' +
        reviewSetId +
        '/batch/' +
        batchId +
        '/document/reviewed',
      requestModel
    )
  }
  public fetchReviewSetBatchInfo$<T>(
    projectId: number,
    reviewSetId: number,
    batchId: number
  ): Observable<T> {
    return this.http.get<T>(
      this._apiUrl +
        '/project/' +
        projectId +
        '/reviewset/' +
        reviewSetId +
        '/batch/' +
        batchId
    )
  }

  public isBatchReviewCompleted$<T>(
    projectId: number,
    reviewSetId: number,
    batchId: number
  ): Observable<T> {
    return this.http.get<T>(
      this._apiUrl +
        '/project/' +
        projectId +
        '/reviewset/' +
        reviewSetId +
        '/batch/' +
        batchId +
        '/iscompleted'
    )
  }

  public checkInReviewBatch$<T>(
    projectId: number,
    reviewSetId: number,
    batchId: number
  ): Observable<T> {
    return this.http.get<T>(
      this._apiUrl +
        '/project/' +
        projectId +
        '/reviewset/' +
        reviewSetId +
        '/batch/' +
        batchId +
        '/checkin'
    )
  }
  public reviewedFileStatus$<T>(
    projectId: number,
    reviewStatusRequestModel: ReviewStatusRequestModel
  ): Observable<T> {
    return this.http.post<T>(
      this._apiUrl + `/file/tags/project/${projectId}/UpdateFileStatus`,
      reviewStatusRequestModel
    )
  }
}
