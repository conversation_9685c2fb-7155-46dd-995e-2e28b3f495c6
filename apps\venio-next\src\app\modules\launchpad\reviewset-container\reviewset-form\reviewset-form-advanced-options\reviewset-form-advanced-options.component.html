<div class="t-flex t-flex-row t-gap-4" [formGroup]="reviewSetForm()">
  <div class="t-flex-1 t-flex t-flex-col t-gap-1">
    <kendo-label
      class="t-font-semibold t-text-[#263238] t-mb-2"
      text="Include Options" />
    <ng-container *ngFor="let option of includeOptions">
      <div class="t-flex t-items-start t-gap-1">
        <input
          [formControlName]="option.controlName"
          type="checkbox"
          kendoCheckBox
          #include />
        <kendo-label
          class="k-checkbox-label"
          [for]="include"
          [text]="option.label" />
      </div>
    </ng-container>

    <kendo-label
      class="t-font-semibold t-mt-4 t-text-[#263238] t-mb-2"
      text="Exclude Options" />
    <ng-container *ngFor="let option of excludeOptions">
      <div class="t-flex t-items-start t-gap-1">
        <input
          type="checkbox"
          [formControlName]="option.controlName"
          kendoCheckBox
          #exclude />
        <kendo-label
          class="k-checkbox-label"
          [for]="exclude"
          [text]="option.label" />
      </div>
    </ng-container>

    <kendo-label
      class="t-font-semibold t-mt-4 t-text-[#263238] t-mb-2"
      text="Review set Propagation Options"></kendo-label>
    <ng-container *ngFor="let option of reviewPropagateOptions">
      <div class="t-flex t-items-start t-gap-1">
        <input
          type="checkbox"
          [formControlName]="option.controlName"
          kendoCheckBox
          #propagate />
        <kendo-label
          class="k-checkbox-label"
          [for]="propagate"
          [text]="option.label" />
      </div>
    </ng-container>
    <div class="t-flex t-flex-col t-gap-2">
      <kendo-label
        class="t-font-semibold t-text-[#263238] t-mt-4 t-items-center"
        text="HTML Auto Job Options" />
      <div class="t-flex t-gap-1 t-w-full t-mt-2">
        <input
          type="checkbox"
          formControlName="autoQueueForHtmlConversion"
          kendoCheckBox
          #nativeDoc />
        <kendo-label
          class="k-checkbox-label t-items-center"
          [for]="nativeDoc"
          text="Convert the native documents to HTML" />
      </div>
    </div>

    <div class="t-flex t-flex-col t-gap-2">
      <kendo-label
        class="t-font-semibold t-text-[#263238] t-mt-4 t-items-center"
        text="Image Auto Job Options" />
      <div class="t-flex t-gap-1 t-w-full t-mt-2">
        <input
          type="checkbox"
          formControlName="autoQueueForTiff"
          kendoCheckBox
          #nativeImage />
        <kendo-label
          class="k-checkbox-label t-items-center"
          [for]="nativeImage"
          text="Convert the native documents to Image" />
      </div>
    </div>
    <div class="t-flex t-flex-col t-gap-2">
      <kendo-label
        class="t-font-semibold t-text-[#263238] t-mt-4 t-items-center"
        text="Review set Options" />
      <div class="t-flex t-gap-1 t-w-full t-mt-2">
        <input
          type="checkbox"
          formControlName="markTaggedDocsAsReviewed"
          kendoCheckBox
          #multipleDoc />
        <kendo-label
          class="k-checkbox-label t-items-center"
          [for]="multipleDoc"
          text="When multiple documents are selected and applied tag, also mark all the document as reviewed" />
      </div>
    </div>
  </div>
  <div class="t-flex t-w-[2px] v-custom-dashed-border t-mx-4"></div>
  <div class="t-flex t-basis-2/5 t-flex-col t-gap-3">
    <div class="t-w-full t-flex t-flex-col t-gap-1">
      <div class="t-flex t-items-center t-mb-1">
        <kendo-label
          class="t-font-semibold t-text-[#263238]"
          text="Tag Propagation Options" />
      </div>
      <div class="t-flex t-flex-col t-gap-2 t-w-full">
        <ng-container *ngFor="let option of tagPropagateOptions">
          <div class="t-flex t-items-center t-gap-1">
            <input
              type="checkbox"
              [formControlName]="option.controlName"
              kendoCheckBox
              #tagPropagate />
            <kendo-label
              class="k-checkbox-label"
              [for]="tagPropagate"
              [text]="option.label" />
          </div>
        </ng-container>
      </div>
      <kendo-label
        class="t-font-semibold t-text-[#263238] t-mt-4 t-items-center"
        text="Continuous Active Learning Options (CAL)" />
      <div class="t-flex t-gap-1 t-w-full t-mt-2">
        <input
          type="checkbox"
          kendoCheckBox
          formControlName="useCALProfileForReviewSet"
          #useCalElement />
        <kendo-label
          class="k-checkbox-label t-items-center"
          [for]="useCalElement"
          text="Use review set for CAL" />
      </div>
      <ng-container
        *ngIf="reviewSetForm()?.controls?.useCALProfileForReviewSet.value">
        <kendo-label
          class="t-font-semibold t-text-[#263238] t-mt-4 t-items-center"
          text="Options" />
        <div class="t-flex t-flex-col t-gap-3">
          <div class="t-flex t-items-center t-justify-between t-gap-2">
            <p>Prediction confidence threshold</p>
            <kendo-numerictextbox
              format="#"
              [min]="0"
              [maxlength]="2"
              formControlName="predictionAccuracyThreshold"
              type="text"
              class="t-w-[90px]">
              <ng-template kendoTextBoxSuffixTemplate>
                <button
                  kendoButton
                  fillMode="outline"
                  class="t-w-[30px] t-bg-[#F1F1F1] t-h-[31px] t-border-[#BEBEBE] t-text-base t-border-solid t-border-l-[1px] t-border-r-[0] t-border-t-[0] t-border-b-[0]">
                  %
                </button>
              </ng-template>
            </kendo-numerictextbox>
          </div>

          <div class="t-flex t-items-center t-justify-between t-gap-2">
            <p>Batch richness threshold</p>
            <kendo-numerictextbox
              format="#"
              [maxlength]="2"
              [min]="0"
              formControlName="batchRichnessThreshold"
              type="text"
              class="t-w-[90px]">
              <ng-template kendoTextBoxSuffixTemplate>
                <button
                  kendoButton
                  fillMode="outline"
                  class="t-w-[30px] t-bg-[#F1F1F1] t-h-[31px] t-border-[#BEBEBE] t-text-base t-border-solid t-border-l-[1px] t-border-r-[0] t-border-t-[0] t-border-b-[0]">
                  %
                </button>
              </ng-template>
            </kendo-numerictextbox>
          </div>
          <div class="t-flex t-items-center t-justify-between t-gap-2">
            <p>Review relevance threshold</p>
            <kendo-numerictextbox
              [min]="0"
              [maxlength]="2"
              format="#"
              formControlName="reviewRelevanceThreshold"
              type="text"
              class="t-w-[90px]">
              <ng-template kendoTextBoxSuffixTemplate>
                <button
                  kendoButton
                  fillMode="outline"
                  class="t-w-[30px] t-bg-[#F1F1F1] t-h-[31px] t-border-[#BEBEBE] t-text-base t-border-solid t-border-l-[1px] t-border-r-[0] t-border-t-[0] t-border-b-[0]">
                  %
                </button>
              </ng-template>
            </kendo-numerictextbox>
          </div>
        </div>
        <div class="t-flex t-gap-1 t-w-full t-mt-2">
          <kendo-checkbox
            #allowUsers
            formControlName="allowReviewAfterCALThreshold"
            class="t-block" />
          <kendo-label
            class="k-checkbox-label t-items-center"
            [for]="allowUsers"
            text="Allow users to review even if batch richness & relevance threshold are met" />
        </div>
        <div class="t-flex t-w-full t-flex-col t-gap-2 t-mt-4">
          <kendo-label
            class="k-checkbox-label t-items-center t-font-semibold t-text-[#263238]"
            [for]="controlSetting"
            text="Control Set Settings" />
          <kendo-dropdownlist
            formControlName="controlSetSizeDerivedBy"
            class="t-w-[210px]"
            valueField="value"
            textField="label"
            [valuePrimitive]="true"
            #controlSetting
            [data]="controlSetOptions" />
        </div>
        <ng-container
          *ngIf="
            reviewSetForm()?.controls?.controlSetSizeDerivedBy.value === 1
          ">
          <div class="t-flex t-flex-col t-gap-3 t-mt-3">
            <div class="t-flex t-items-center t-justify-between t-gap-2">
              <p>Population</p>
              <div class="t-inline-block t-w-[90px] t-text-left">
                {{ documentCount() || 0 }} (Original files only)
              </div>
            </div>
            <div class="t-flex t-items-center t-justify-between t-gap-2">
              <p>Percentage</p>
              <kendo-numerictextbox
                format="#"
                [min]="0"
                formControlName="percentageOfPopulation"
                [maxlength]="2"
                type="text"
                class="t-w-[90px]">
                <ng-template kendoTextBoxSuffixTemplate>
                  <button
                    kendoButton
                    fillMode="outline"
                    class="t-w-[30px] t-bg-[#F1F1F1] t-h-[31px] t-border-[#BEBEBE] t-text-base t-border-solid t-border-l-[1px] t-border-r-[0] t-border-t-[0] t-border-b-[0]">
                    %
                  </button>
                </ng-template>
              </kendo-numerictextbox>
            </div>
            <div class="t-flex t-items-center t-justify-between t-gap-2">
              <p>Control Set Size (Max)</p>
              <div class="t-inline-block t-w-[90px] t-text-left">
                {{ documentCount() || 0 }}
              </div>
            </div>
          </div>
        </ng-container>
        <ng-container
          *ngIf="
            reviewSetForm()?.controls?.controlSetSizeDerivedBy.value === 0
          ">
          <div class="t-flex t-flex-col t-gap-3 t-mt-3">
            <div class="t-flex t-items-center t-justify-between t-gap-2">
              <p>Population</p>
              <div class="t-inline-block t-w-[90px] t-text-left">
                {{ documentCount() || 0 }} (Original files only)
              </div>
            </div>
            <div class="t-flex t-items-center t-justify-between t-gap-2">
              <p>Confidence level</p>
              <kendo-numerictextbox
                format="#"
                [min]="0"
                formControlName="confidenceLevel"
                [maxlength]="2"
                type="text"
                class="t-w-[90px]">
                <ng-template kendoTextBoxSuffixTemplate>
                  <button
                    kendoButton
                    fillMode="outline"
                    class="t-w-[30px] t-bg-[#F1F1F1] t-h-[31px] t-border-[#BEBEBE] t-text-base t-border-solid t-border-l-[1px] t-border-r-[0] t-border-t-[0] t-border-b-[0]">
                    %
                  </button>
                </ng-template>
              </kendo-numerictextbox>
            </div>
            <div class="t-flex t-items-center t-justify-between t-gap-2">
              <p>Confidence interval</p>
              <kendo-numerictextbox
                format="#"
                [min]="0"
                formControlName="confidenceInterval"
                [maxlength]="2"
                type="text"
                class="t-w-[90px]">
                <ng-template kendoTextBoxSuffixTemplate>
                  <button
                    kendoButton
                    fillMode="outline"
                    class="t-w-[30px] t-bg-[#F1F1F1] t-h-[31px] t-border-[#BEBEBE] t-text-base t-border-solid t-border-l-[1px] t-border-r-[0] t-border-t-[0] t-border-b-[0]">
                    %
                  </button>
                </ng-template>
              </kendo-numerictextbox>
            </div>
            <div class="t-flex t-items-center t-justify-between t-gap-2">
              <p>Control Set Size (Max)</p>
              <div class="t-inline-block t-w-[90px] t-text-left">
                {{ documentCount() || 0 }}
              </div>
            </div>
          </div>
        </ng-container>
        <ng-container
          *ngIf="
            reviewSetForm()?.controls?.controlSetSizeDerivedBy.value === 2
          ">
          <div class="t-flex t-flex-col t-gap-3 t-mt-3">
            <div class="t-flex t-items-center t-justify-between t-gap-2">
              <p>Population</p>
              <div class="t-inline-block t-w-[90px] t-text-left">
                {{ documentCount() || 0 }} (Original files only)
              </div>
            </div>
            <div class="t-flex t-items-center t-justify-between t-gap-2">
              <p>Number</p>
              <kendo-numerictextbox
                [min]="0"
                formControlName="numberOfDocuments"
                format="#"
                class="t-w-[90px]"
                [step]="1" />
            </div>
            <div class="t-flex t-items-center t-justify-between t-gap-2">
              <p>Control Set Size (Max)</p>
              <div class="t-inline-block t-w-[90px] t-text-left">
                {{ documentCount() || 0 }}
              </div>
            </div>
          </div>
        </ng-container>
        <div class="t-flex t-w-full t-flex-col t-gap-2 t-mt-4">
          <kendo-label
            class="k-checkbox-label t-items-center t-font-semibold t-text-[#263238]"
            [for]="controlSettingFor"
            text="Control Set Format" />
          <kendo-dropdownlist
            formControlName="isDynamicControlSet"
            textField="label"
            valueField="value"
            [valuePrimitive]="true"
            class="t-w-[210px]"
            #controlSettingFor
            [data]="controlSetFormatOptions" />
        </div>
        <div
          class="t-flex t-flex-col t-gap-2 t-mt-3"
          *ngIf="!reviewSetForm()?.controls?.isDynamicControlSet.value">
          <kendo-label
            class="k-checkbox-label t-items-center t-font-semibold t-text-[#263238]"
            text="Min & Max Training Set Primary Doc Count" />
          <div class="t-flex t-gap-3">
            <kendo-numerictextbox
              [min]="0"
              formControlName="calTrainingSetMinDocCount"
              format="#"
              class="t-w-[90px]"
              [step]="1" />
            <kendo-numerictextbox
              formControlName="calTrainingSetMaxDocCount"
              format="#"
              class="t-w-[90px]"
              [step]="1" />
          </div>
        </div>

        <div
          class="t-flex t-w-full t-flex-col t-gap-2 t-mt-4"
          *ngIf="reviewSetForm()?.controls?.isDynamicControlSet.value">
          <kendo-label
            class="k-checkbox-label t-items-center t-font-semibold t-text-[#263238]"
            [for]="controlSettingFor"
            text="Control Set Quota"></kendo-label>

          <div class="t-flex t-items-center t-justify-between t-gap-2">
            <p>% from each Training Batch</p>

            <kendo-numerictextbox
              format="#"
              formControlName="controlSetPercentFromTrainingBatch"
              [min]="1"
              [max]="5"
              class="t-w-[90px]">
              <ng-template kendoTextBoxSuffixTemplate>
                <button
                  kendoButton
                  fillMode="outline"
                  class="t-w-[30px] t-bg-[#F1F1F1] t-h-[31px] t-border-[#BEBEBE] t-text-base t-border-solid t-border-l-[1px] t-border-r-[0] t-border-t-[0] t-border-b-[0]">
                  %
                </button>
              </ng-template>
            </kendo-numerictextbox>
          </div>

          <div class="t-flex t-flex-col t-gap-2 t-mt-3">
            <kendo-label
              class="k-checkbox-label t-items-center t-font-semibold t-text-[#263238]"
              text="Min & Max Control Set Primary Doc Count"></kendo-label>
            <div class="t-flex t-gap-3">
              <kendo-numerictextbox
                [min]="0"
                format="#"
                class="t-w-[90px]"
                placeholder="Min"
                formControlName="calControlSetMinDocCount"
                [step]="1" />
              <kendo-numerictextbox
                [min]="0"
                format="#"
                placeholder="Max"
                formControlName="calControlSetMaxDocCount"
                class="t-w-[90px]"
                [step]="1" />
            </div>
          </div>
          <div class="t-flex t-flex-col t-gap-2 t-mt-3">
            <kendo-label
              class="k-checkbox-label t-items-center t-font-semibold t-text-[#263238]"
              text="Min & Max Training Set Primary Doc Count"></kendo-label>
            <div class="t-flex t-gap-3">
              <kendo-numerictextbox
                format="#"
                [min]="0"
                placeholder="Min"
                class="t-w-[90px]"
                formControlName="calTrainingSetMinDocCount"
                [step]="1" />
              <kendo-numerictextbox
                format="#"
                [min]="0"
                placeholder="Max"
                formControlName="calTrainingSetMaxDocCount"
                class="t-w-[90px]"
                [step]="1" />
            </div>
          </div>
        </div>
      </ng-container>
    </div>
  </div>
</div>
